import {
    abbreviateFilenames,
    generateMonitorTestReportFileName,
    getMimeTypeFromFileTypes,
    makeFileNamesUniqueByEnumeration,
    pathResolve,
    removeDuplicatedFiles,
    sanitizeFileName,
} from 'commons/helpers/file.helper';
import { FileBufferType } from 'commons/types/file-buffer.type';
import { SymlinkType } from 'commons/types/symlink.type';
import config from 'config';
import moment from 'moment';
import path from 'path';

describe('file helper', () => {
    test('change / by -', () => {
        expect(sanitizeFileName('evidence/library 215')).toEqual('evidence-library-215');
    });

    test('removes special characters', () => {
        expect(sanitizeFileName('evidence/library @215?:!')).not.toContain('@!:?/');
    });

    test('removes special characters keeping the file extension', () => {
        const filename = 'evidence/library @215?:!.pdf';
        const sanitizedName = sanitizeFileName(filename);
        expect(sanitizedName).not.toContain('@!:?/');
        expect(sanitizedName).toContain('.pdf');
    });

    test('file path is an EMPTY string if nothing was passed in', () => {
        const output = pathResolve('');
        expect(output).toEqual('');
    });

    test('file path is as expected without leading slash', () => {
        const prefix = config.get('api.pathPrefix');
        const output = pathResolve('test/path.ts');
        expect(output).toEqual(path.resolve(`${prefix}/test/path.ts`));
    });

    test('file path is as expected with leading slash', () => {
        const prefix = config.get('api.pathPrefix');
        const output = pathResolve('/test/path.ts');
        expect(output).toEqual(path.resolve(`${prefix}/test/path.ts`));
    });
});

describe('makeFileNamesUniqueByEnumeration', () => {
    test('Given a list of file buffers with unique names, it should return with the original name', () => {
        const testFileBuffers: FileBufferType[] = [
            {
                filename: 'sampleFile.doc',
                stream: Buffer.alloc(0),
            },
            {
                filename: 'sampleFile.pdf',
                stream: Buffer.alloc(0),
            },
        ];
        expect(makeFileNamesUniqueByEnumeration(testFileBuffers)).toEqual(testFileBuffers);
    });

    describe('Given a list of file buffers with the same names', () => {
        const testFileBuffers: FileBufferType[] = [
            {
                filename: 'sampleFile.doc',
                stream: Buffer.alloc(0),
            },
            {
                filename: 'sampleFile.doc',
                stream: Buffer.alloc(0),
            },
            {
                filename: 'sampleFile.doc',
                stream: Buffer.alloc(0),
            },
        ];

        it('should return with enumerated names', () => {
            expect(makeFileNamesUniqueByEnumeration(testFileBuffers)).toEqual([
                {
                    filename: 'sampleFile.doc',
                    stream: Buffer.alloc(0),
                },
                {
                    filename: 'sampleFile_1.doc',
                    stream: Buffer.alloc(0),
                },
                {
                    filename: 'sampleFile_2.doc',
                    stream: Buffer.alloc(0),
                },
            ]);
        });
    });
});

describe('getMimeTypeFromFileTypes', () => {
    it('should return the correct MIME type for a known file extension', () => {
        expect(getMimeTypeFromFileTypes('document.pdf')).toBe('application/pdf');
        expect(getMimeTypeFromFileTypes('image.jpg')).toBe('image/jpeg');
        expect(getMimeTypeFromFileTypes('data.json')).toBe('application/json');
    });

    it('should return null for files without an extension', () => {
        expect(getMimeTypeFromFileTypes('file')).toBeNull();
    });

    it('should return null for unknown file extensions', () => {
        expect(getMimeTypeFromFileTypes('unknownfile.xyz')).toBeNull();
    });

    it('should handle uppercase file extensions correctly', () => {
        expect(getMimeTypeFromFileTypes('IMAGE.JPG')).toBe('image/jpeg');
        expect(getMimeTypeFromFileTypes('Document.PDF')).toBe('application/pdf');
    });

    it('should return null if the file extension is empty after a dot', () => {
        expect(getMimeTypeFromFileTypes('file.')).toBeNull();
    });

    it('should handle file names with multiple dots correctly', () => {
        expect(getMimeTypeFromFileTypes('archive.tar.gz')).toBeNull();
        expect(getMimeTypeFromFileTypes('project.report.pdf')).toBe('application/pdf');
    });
});

describe('abbreviateFilenames', () => {
    // Type assertion function for manifest files
    function assertManifestExists(
        manifest: { stream: string; filename: string } | undefined,
    ): asserts manifest is { stream: string; filename: string } {
        expect(manifest).toBeDefined();
    }

    it('should not modify files when no ControlEvidence/Evidence/ files are present', () => {
        const evidenceFilesJson = [
            {
                filename: 'short/path/file.txt',
                stream: 'base64content1',
            },
            {
                filename: 'another/short/file.pdf',
                stream: 'base64content2',
            },
        ];

        abbreviateFilenames(evidenceFilesJson, []);

        // Function always adds manifest, so length increases by 1
        expect(evidenceFilesJson).toHaveLength(3);
        expect(evidenceFilesJson[0].filename).toBe('short/path/file.txt');
        expect(evidenceFilesJson[1].filename).toBe('another/short/file.pdf');

        // Should have manifest with "No abbreviated files found" message
        const manifest = evidenceFilesJson.find(file => file.filename === 'evidence_manifest.html');
        assertManifestExists(manifest);

        const manifestContent = Buffer.from(manifest.stream, 'base64').toString();
        expect(manifestContent).toContain('No abbreviated files found');
    });

    it('should abbreviate ControlEvidence/Evidence/ files to numbered filenames', () => {
        const evidenceFilesJson = [
            {
                filename: 'ControlEvidence/Evidence/some/path/document.pdf',
                stream: 'base64content1',
            },
            {
                filename: 'ControlEvidence/Evidence/another/path/image.jpg',
                stream: 'base64content2',
            },
        ];

        abbreviateFilenames(evidenceFilesJson, []);

        expect(evidenceFilesJson).toHaveLength(3); // 2 files + manifest
        expect(evidenceFilesJson[0].filename).toBe('ControlEvidence/Evidence/1.pdf');
        expect(evidenceFilesJson[1].filename).toBe('ControlEvidence/Evidence/2.jpg');

        // Should have manifest
        const manifest = evidenceFilesJson.find(file => file.filename === 'evidence_manifest.html');
        assertManifestExists(manifest);
    });

    it('should preserve file extensions when abbreviating', () => {
        const evidenceFilesJson = [
            {
                filename: 'ControlEvidence/Evidence/path/document.complex.extension.tar.gz',
                stream: 'base64content',
            },
        ];

        abbreviateFilenames(evidenceFilesJson, []);

        // path.extname() only gets the last extension (.gz), not the full extension (.tar.gz)
        expect(evidenceFilesJson[0].filename).toBe('ControlEvidence/Evidence/1.gz');
        expect(evidenceFilesJson).toHaveLength(2); // 1 file + manifest
    });

    it('should handle simple file extensions correctly', () => {
        const evidenceFilesJson = [
            {
                filename: 'ControlEvidence/Evidence/path/document.pdf',
                stream: 'base64content',
            },
        ];

        abbreviateFilenames(evidenceFilesJson, []);

        expect(evidenceFilesJson[0].filename).toBe('ControlEvidence/Evidence/1.pdf');
        expect(evidenceFilesJson).toHaveLength(2); // 1 file + manifest
    });

    it('should handle files without extensions', () => {
        const evidenceFilesJson = [
            {
                filename: 'ControlEvidence/Evidence/controlName/document_without_extension',
                stream: 'base64content',
            },
        ];

        abbreviateFilenames(evidenceFilesJson, []);

        expect(evidenceFilesJson[0].filename).toBe('ControlEvidence/Evidence/1');
        expect(evidenceFilesJson).toHaveLength(2); // 1 file + manifest

        // Should have manifest
        const manifest = evidenceFilesJson.find(file => file.filename === 'evidence_manifest.html');
        assertManifestExists(manifest);

        // Verify it's HTML content
        const manifestContent = Buffer.from(manifest.stream, 'base64').toString();
        expect(manifestContent).toContain('<!DOCTYPE html>');
        expect(manifestContent).toContain('document_without_extension');
    });

    it('should sort files by DCF number when present', () => {
        const evidenceFilesJson = [
            {
                filename: 'ControlEvidence/Evidence/DCF3-third.pdf',
                stream: 'content3',
            },
            {
                filename: 'ControlEvidence/Evidence/DCF1-first.pdf',
                stream: 'content1',
            },
            {
                filename: 'ControlEvidence/Evidence/DCF2-second.pdf',
                stream: 'content2',
            },
        ];

        abbreviateFilenames(evidenceFilesJson, []);

        expect(evidenceFilesJson).toHaveLength(4); // 3 files + manifest
        expect(evidenceFilesJson[0].filename).toBe('ControlEvidence/Evidence/1.pdf');
        expect(evidenceFilesJson[0].stream).toBe('content1'); // DCF1 should be first
        expect(evidenceFilesJson[1].filename).toBe('ControlEvidence/Evidence/2.pdf');
        expect(evidenceFilesJson[1].stream).toBe('content2'); // DCF2 should be second
        expect(evidenceFilesJson[2].filename).toBe('ControlEvidence/Evidence/3.pdf');
        expect(evidenceFilesJson[2].stream).toBe('content3'); // DCF3 should be third
    });

    it('should generate HTML manifest with correct mapping', () => {
        const evidenceFilesJson = [
            {
                filename: 'ControlEvidence/Evidence/some/controlName/document.pdf',
                stream: 'base64content',
            },
        ];

        abbreviateFilenames(evidenceFilesJson, []);

        const manifest = evidenceFilesJson.find(file => file.filename === 'evidence_manifest.html');
        assertManifestExists(manifest);

        const manifestContent = Buffer.from(manifest.stream, 'base64').toString();
        expect(manifestContent).toContain('<!DOCTYPE html>');
        expect(manifestContent).toContain('Evidence Manifest');
        expect(manifestContent).toContain('controlName/document.pdf');
        expect(manifestContent).toContain('1.pdf');
    });

    it('should handle mixed files (some with ControlEvidence/Evidence/, some without)', () => {
        const evidenceFilesJson = [
            {
                filename: 'regular/file.txt',
                stream: 'content1',
            },
            {
                filename: 'ControlEvidence/Evidence/evidence.pdf',
                stream: 'content2',
            },
            {
                filename: 'another/regular/file.doc',
                stream: 'content3',
            },
        ];

        abbreviateFilenames(evidenceFilesJson, []);

        expect(evidenceFilesJson).toHaveLength(4); // 3 original + 1 manifest
        expect(evidenceFilesJson[0].filename).toBe('regular/file.txt'); // unchanged
        expect(evidenceFilesJson[1].filename).toBe('ControlEvidence/Evidence/1.pdf'); // abbreviated
        expect(evidenceFilesJson[2].filename).toBe('another/regular/file.doc'); // unchanged

        const manifest = evidenceFilesJson.find(file => file.filename === 'evidence_manifest.html');
        assertManifestExists(manifest);
    });

    it('should handle empty filename', () => {
        const evidenceFilesJson = [
            {
                filename: '',
                stream: 'content',
            },
        ];

        abbreviateFilenames(evidenceFilesJson, []);

        // Function always adds manifest, so length increases by 1
        expect(evidenceFilesJson).toHaveLength(2);
        expect(evidenceFilesJson[0].filename).toBe('');

        // Should have manifest with "No abbreviated files found" message
        const manifest = evidenceFilesJson.find(file => file.filename === 'evidence_manifest.html');
        assertManifestExists(manifest);

        const manifestContent = Buffer.from(manifest.stream, 'base64').toString();
        expect(manifestContent).toContain('No abbreviated files found');
    });

    it('should handle symlinks and create requirement-specific manifests', () => {
        const evidenceFilesJson = [
            {
                filename: 'ControlEvidence/Evidence/Personnel Evidence/employee-handbook.pdf',
                stream: 'content1',
            },
            {
                filename: 'ControlEvidence/Evidence/Vendor Info/vendor-contract.pdf',
                stream: 'content2',
            },
        ];

        const symlinks: SymlinkType[] = [
            {
                link: 'ControlEvidence/SOC2/CC1.1/Personnel Evidence',
                original: '../../../Personnel Evidence',
            },
            {
                link: 'ControlEvidence/SOC2/CC1.2/Vendor Info',
                original: '../../../Vendor Info',
            },
        ];

        abbreviateFilenames(evidenceFilesJson, symlinks);

        // Should have original files + main manifest + requirement manifests
        expect(evidenceFilesJson.length).toBeGreaterThan(3);

        // Check main manifest exists
        const mainManifest = evidenceFilesJson.find(
            file => file.filename === 'evidence_manifest.html',
        );
        assertManifestExists(mainManifest);

        // Check requirement-specific manifests exist
        const cc11Manifest = evidenceFilesJson.find(
            file => file.filename === 'ControlEvidence/SOC2/CC1.1/CC1.1-evidence_manifest.html',
        );
        const cc12Manifest = evidenceFilesJson.find(
            file => file.filename === 'ControlEvidence/SOC2/CC1.2/CC1.2-evidence_manifest.html',
        );

        assertManifestExists(cc11Manifest);
        assertManifestExists(cc12Manifest);

        // Verify requirement manifest content
        const cc11Content = Buffer.from(cc11Manifest.stream, 'base64').toString();
        expect(cc11Content).toContain('CC1.1 - Evidence Manifest');
        expect(cc11Content).toContain('../../../ControlEvidence/Evidence/');
    });

    it('should handle symlinks pointing to ControlEvidence/Evidence/', () => {
        const evidenceFilesJson = [
            {
                filename: 'ControlEvidence/Evidence/Personnel Evidence/employee-handbook.pdf',
                stream: 'content1',
            },
        ];

        const symlinks: SymlinkType[] = [
            {
                link: 'ControlEvidence/Evidence/CC1.1/Personnel Evidence',
                original: '../Personnel Evidence',
            },
        ];

        abbreviateFilenames(evidenceFilesJson, symlinks);

        // Should have abbreviated files + main manifest
        expect(evidenceFilesJson).toHaveLength(2);
        expect(evidenceFilesJson[0].filename).toBe('ControlEvidence/Evidence/1.pdf');

        // Check main manifest includes symlink information
        const mainManifest = evidenceFilesJson.find(
            file => file.filename === 'evidence_manifest.html',
        );
        assertManifestExists(mainManifest);

        const manifestContent = Buffer.from(mainManifest.stream, 'base64').toString();
        expect(manifestContent).toContain('CC1.1');
        expect(manifestContent).toContain('Personnel Evidence/employee-handbook.pdf');
    });
});

describe('removeDuplicatedFiles', () => {
    it('should return an empty array when given an empty array', () => {
        const result = removeDuplicatedFiles([]);
        expect(result).toEqual([]);
    });

    it('should return the same array when there are no duplicates', () => {
        const files = [
            { filename: 'file1.txt', stream: 'content1' },
            { filename: 'file2.pdf', stream: 'content2' },
            { filename: 'file3.doc', stream: 'content3' },
        ];

        const result = removeDuplicatedFiles(files);
        expect(result).toEqual(files);
        expect(result).toHaveLength(3);
    });

    it('should remove duplicate files and keep the first occurrence', () => {
        const files = [
            { filename: 'document.pdf', stream: 'first-content' },
            { filename: 'image.jpg', stream: 'image-content' },
            { filename: 'document.pdf', stream: 'second-content' },
            { filename: 'text.txt', stream: 'text-content' },
            { filename: 'document.pdf', stream: 'third-content' },
        ];

        const result = removeDuplicatedFiles(files);

        expect(result).toHaveLength(3);
        expect(result).toEqual([
            { filename: 'document.pdf', stream: 'first-content' },
            { filename: 'image.jpg', stream: 'image-content' },
            { filename: 'text.txt', stream: 'text-content' },
        ]);
    });

    it('should handle files with identical filenames but different content', () => {
        const files = [
            { filename: 'report.xlsx', stream: 'version1' },
            { filename: 'report.xlsx', stream: 'version2' },
            { filename: 'report.xlsx', stream: 'version3' },
        ];

        const result = removeDuplicatedFiles(files);

        expect(result).toHaveLength(1);
        expect(result[0]).toEqual({ filename: 'report.xlsx', stream: 'version1' });
    });

    it('should preserve order of unique files', () => {
        const files = [
            { filename: 'z-file.txt', stream: 'z-content' },
            { filename: 'a-file.txt', stream: 'a-content' },
            { filename: 'm-file.txt', stream: 'm-content' },
            { filename: 'a-file.txt', stream: 'duplicate-a' },
        ];

        const result = removeDuplicatedFiles(files);

        expect(result).toHaveLength(3);
        expect(result.map(f => f.filename)).toEqual(['z-file.txt', 'a-file.txt', 'm-file.txt']);
        expect(result[1].stream).toBe('a-content'); // First occurrence preserved
    });

    it('should handle files with special characters in filenames', () => {
        const files = [
            { filename: 'file with spaces.txt', stream: 'content1' },
            { filename: 'file-with-dashes.pdf', stream: 'content2' },
            { filename: 'file with spaces.txt', stream: 'duplicate-content' },
            { filename: 'file_with_underscores.doc', stream: 'content3' },
        ];

        const result = removeDuplicatedFiles(files);

        expect(result).toHaveLength(3);
        expect(result.find(f => f.filename === 'file with spaces.txt')?.stream).toBe('content1');
    });

    it('should handle single file array', () => {
        const files = [{ filename: 'single-file.txt', stream: 'single-content' }];

        const result = removeDuplicatedFiles(files);

        expect(result).toEqual(files);
        expect(result).toHaveLength(1);
    });

    it('should handle files with empty filenames', () => {
        const files = [
            { filename: '', stream: 'content1' },
            { filename: 'normal-file.txt', stream: 'content2' },
            { filename: '', stream: 'content3' },
        ];

        const result = removeDuplicatedFiles(files);

        expect(result).toHaveLength(2);
        expect(result[0]).toEqual({ filename: '', stream: 'content1' });
        expect(result[1]).toEqual({ filename: 'normal-file.txt', stream: 'content2' });
    });

    it('should handle files with very long filenames', () => {
        const longFilename = 'very-long-filename-'.repeat(20) + '.txt';
        const files = [
            { filename: longFilename, stream: 'content1' },
            { filename: 'short.txt', stream: 'content2' },
            { filename: longFilename, stream: 'duplicate-content' },
        ];

        const result = removeDuplicatedFiles(files);

        expect(result).toHaveLength(2);
        expect(result[0].filename).toBe(longFilename);
        expect(result[0].stream).toBe('content1');
    });

    describe('generateMonitorTestReportFileName', () => {
        it('should generate correct filename for included type', () => {
            const testDate = moment('2024-01-15').toDate();
            const result = generateMonitorTestReportFileName(
                'included',
                123,
                'Test Name',
                testDate,
            );
            expect(result).toBe('Failing-Resources-For-Test-123-Test-Name-01152024');
        });

        it('should generate correct filename for excluded type', () => {
            const testDate = moment('2024-01-15').toDate();
            const result = generateMonitorTestReportFileName(
                'excluded',
                456,
                'Another Test',
                testDate,
            );
            expect(result).toBe('Excluded-Results-For-Test-456-Another-Test-01152024');
        });

        it('should sanitize test name with special characters', () => {
            const testDate = moment('2024-01-15').toDate();
            const result = generateMonitorTestReportFileName(
                'included',
                789,
                'Test/Name @Special!',
                testDate,
            );
            expect(result).toBe('Failing-Resources-For-Test-789-Test-Name-Special-01152024');
        });

        it('should use current date when no date provided', () => {
            const result = generateMonitorTestReportFileName('included', 123, 'Test Name');
            // Should contain today's date in MMddyyyy format
            expect(result).toMatch(/^Failing-Resources-For-Test-123-Test-Name-\d{8}$/);
        });
    });
});
